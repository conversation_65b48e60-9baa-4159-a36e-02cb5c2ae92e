import mongoose from "mongoose";

const aegisGraderSchema = new mongoose.Schema({
    testDetails: {
        createdBy: { type: String, required: true },
        className: { type: String, required: true },
        subject: { type: String, required: true },
        date: { type: String, required: true },
    },
    answerSheets: [
        {
            id: { type: String, required: true },
            studentName: { type: String, required: true },
            rollNumber: { type: String, required: true },
            pdfUrl: { type: String, required: true },
            timestamp: { type: Number, required: true },
            evaluationResult: { type: mongoose.Schema.Types.Mixed },
            status: {
                type: String,
                enum: ['pending', 'processing', 'evaluating', 'completed', 'error'],
                default: 'pending'
            },
            processedAt: { type: Date },
            // Track refund status for individual sheets
            refundStatus: {
                isRefunded: { type: Boolean, default: false },
                refundTransactionId: { type: String },
                refundedAt: { type: Date }
            }
        },
    ],
    // New field for overall processing statistics
    processingStats: {
        totalAnswerSheets: { type: Number, default: 0 },
        successfulEvaluations: { type: Number, default: 0 },
        failedEvaluations: { type: Number, default: 0 },
        completedAt: { type: Date },
        processingStartedAt: { type: Date },
        overallStatus: {
            type: String,
            enum: ['pending', 'processing', 'evaluating', 'completed', 'partial_completion'],
            default: 'pending'
        }
    },
    // Credit tracking for refunds
    creditInfo: {
        totalCreditsCharged: { type: Number, default: 0 },
        creditsRefunded: { type: Number, default: 0 },
        originalTransactionId: { type: String }, // Reference to the original credit deduction transaction
        refundTransactionIds: [{ type: String }] // Array of refund transaction IDs
    },
    questionPaper: {
        type: {
            type: String,
            enum: ['rubric', 'questionPaper'],
            required: false,
        },
        pdfUrl: { type: String, required: false },
        timestamp: { type: Number, required: false },
    },
    rubric: {
        type: {
            type: String,
            enum: ['rubric', 'questionPaper'],
            required: false,
        },
        pdfUrl: { type: String, required: false },
        timestamp: { type: Number, required: false },
    }
}, { collection: "AegisGrader", timestamps: true });

// Indexes for better query performance
aegisGraderSchema.index({ timestamp: -1 });
aegisGraderSchema.index({ "testDetails.createdBy": 1, createdAt: -1 });
aegisGraderSchema.index({ "processingStats.overallStatus": 1 });
aegisGraderSchema.index({ "creditInfo.originalTransactionId": 1 });

const AegisGrader = mongoose.model("AegisGrader", aegisGraderSchema);
export default AegisGrader;