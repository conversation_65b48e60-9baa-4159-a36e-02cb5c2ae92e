import React from 'react';
import { Status, StatusUtils } from '../../types/aegisGrader';

export const GradingProgressBar: React.FC<{
    value: number;
    status?: string;
    className?: string;
}> = ({ value, status, className = "" }) => {
    const normalizedStatus = StatusUtils.normalize(status);

    const getProgressBarStyle = () => {
        switch (normalizedStatus) {
            case Status.COMPLETED:
                return {
                    background: 'linear-gradient(90deg, #10b981, #059669, #10b981)',
                    backgroundSize: '200% 100%',
                    animation: 'none'
                };
            case Status.PROCESSING:
            case Status.EVALUATING:
                return {
                    background: 'linear-gradient(90deg, #40ffaa, #4079ff, #40ffaa, #4079ff)',
                    backgroundSize: '300% 100%',
                    animation: 'gradient 3s ease-in-out infinite'
                };
            case Status.ERROR:
                return {
                    background: 'linear-gradient(90deg, #ef4444, #dc2626, #ef4444)',
                    backgroundSize: '200% 100%',
                    animation: 'gradient 2s ease-in-out infinite'
                };
            case Status.PENDING:
            default:
                return {
                    background: 'hsl(var(--muted-foreground) / 0.5)',
                    backgroundSize: '100% 100%',
                    animation: 'none'
                };
        }
    };

    const getBackgroundColor = () => {
        switch (normalizedStatus) {
            case Status.COMPLETED:
                return 'bg-success-100 dark:bg-success-900/20';
            case Status.PROCESSING:
            case Status.EVALUATING:
                return 'bg-primary/10 dark:bg-primary/20';
            case Status.ERROR:
                return 'bg-destructive/20';
            case Status.PENDING:
            default:
                return 'bg-secondary';
        }
    };

    const progressStyle = getProgressBarStyle();

    return (
        <div className={`relative h-2 w-full overflow-hidden rounded-full ${getBackgroundColor()} ${className}`}>
            <div
                className="h-full transition-all duration-500 ease-in-out rounded-full"
                style={{
                    width: `${Math.max(0, Math.min(100, value))}%`,
                    ...progressStyle
                }}
            />
            {/* Add a subtle glow effect for processing and evaluating states */}
            {(normalizedStatus === Status.PROCESSING || normalizedStatus === Status.EVALUATING) && (
                <div
                    className="absolute inset-0 rounded-full opacity-30"
                    style={{
                        background: 'linear-gradient(90deg, transparent, rgba(64, 255, 170, 0.3), rgba(64, 121, 255, 0.3), transparent)',
                        backgroundSize: '200% 100%',
                        animation: 'gradient 2s ease-in-out infinite',
                        width: `${Math.max(0, Math.min(100, value))}%`
                    }}
                />
            )}
        </div>
    );
};