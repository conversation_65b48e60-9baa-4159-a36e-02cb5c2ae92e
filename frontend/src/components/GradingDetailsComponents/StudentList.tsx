import React from 'react';
import { MagnifyingGlassIcon, UsersIcon } from '@heroicons/react/24/outline';
import { AnswerSheetData, AnswerSheetResult, SubmissionData } from '../../types/gradingTypes';
import { StudentCard } from './StudentCard';

interface StudentListProps {
    sheets: AnswerSheetData[];
    searchTerm: string;
    onSearchChange: (term: string) => void;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (sheet: AnswerSheetData) => AnswerSheetResult | null;
    onRefreshData?: () => void;
    submissionId?: string;
}

export const StudentList: React.FC<StudentListProps> = ({
    sheets,
    searchTerm,
    onSearchChange,
    onViewResults,
    formatResults,
    onRefreshData,
    submissionId
}) => {
    return (
        <div className="lg:col-span-3 order-1 lg:order-1">
            <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border overflow-hidden flex flex-col h-full">
                {/* Header Section */}
                <div className="p-3 sm:p-4 border-b border-border flex-shrink-0">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                        <h2 className="text-base sm:text-lg font-semibold flex items-center gap-2 text-foreground">
                            <UsersIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                            Students ({sheets.length})
                        </h2>
                        
                        {/* Search Input */}
                        <div className="relative w-full sm:w-auto sm:min-w-[240px]">
                            <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <input
                                type="text"
                                placeholder="Search students..."
                                className="w-full pl-10 pr-4 py-2 sm:py-2.5 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none text-sm sm:text-base"
                                value={searchTerm}
                                onChange={(e) => onSearchChange(e.target.value)}
                            />
                        </div>
                    </div>
                </div>

                {/* Students List - Scrollable Container */}
                <div className="flex-1 overflow-hidden">
                    <div className="h-full max-h-[calc(100vh-300px)] overflow-y-auto">
                        <div className="p-3 sm:p-4 space-y-2 sm:space-y-3">
                            {sheets.length === 0 ? (
                                <div className="text-center py-8 text-muted-foreground">
                                    <UsersIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
                                    <p className="text-sm sm:text-base">
                                        {searchTerm ? 'No students found matching your search' : 'No students found'}
                                    </p>
                                    {searchTerm && (
                                        <p className="text-xs sm:text-sm mt-1 opacity-75">
                                            Try adjusting your search terms
                                        </p>
                                    )}
                                </div>
                            ) : (
                                sheets.map((sheet) => (
                                    <StudentCard
                                        key={sheet.id}
                                        sheet={sheet}
                                        onViewResults={onViewResults}
                                        formatResults={formatResults}
                                        onRefreshData={onRefreshData}
                                        submissionId={submissionId}
                                    />
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
