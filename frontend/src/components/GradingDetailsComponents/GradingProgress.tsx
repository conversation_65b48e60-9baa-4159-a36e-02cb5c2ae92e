import { Status, StatusUtils } from '../../types/aegisGrader';
import GradientText from '../GradientText';
import { GradingProgressBar } from './GradingProgressBar';

interface GradingProgressProps {
    progress: number;
    status: string;
    isConnected: boolean;
    error: string | null;
}

export const GradingProgress: React.FC<GradingProgressProps> = ({
    progress,
    status,
    isConnected,
    error
}) => {
    const normalizedStatus = StatusUtils.normalize(status);
    const hasError = normalizedStatus === Status.ERROR || !!error;
    const isCompleted = normalizedStatus === Status.COMPLETED;
    const isGrading = normalizedStatus === Status.PROCESSING;
    const isEvaluating = normalizedStatus === Status.EVALUATING;

    const getStatusText = () => {
        if (hasError) return 'Grading Failed';
        if (isCompleted) return 'Grading Complete';
        if (isGrading) return 'Grading in Progress';
        if (isEvaluating) return 'Evaluating Answers';
        return 'Pending Grading';
    };

    const getStatusColorClass = () => {
        if (hasError) return 'text-destructive';
        if (isCompleted) return 'text-success-600 dark:text-success-400';
        if (isGrading) return 'text-primary';
        if (isEvaluating) return 'text-purple-600 dark:text-purple-400';
        return 'text-muted-foreground';
    };

    return (
        <div className="w-full">
            <div className="flex items-center justify-between mb-1">
                <span className="text-xs font-medium flex items-center">
                    {(isGrading || isEvaluating) ? (
                        <GradientText
                            colors={["#40ffaa", "#4079ff", "#40ffaa", "#4079ff", "#40ffaa"]}
                            animationSpeed={3}
                            showBorder={false}
                            className="text-xs"
                        >
                            {getStatusText()}
                        </GradientText>
                    ) : (
                        <span className={getStatusColorClass()}>
                            {getStatusText()}
                        </span>
                    )}
                    {isConnected && (isGrading || isEvaluating) && (
                        <span className="ml-2 inline-block w-2 h-2 bg-primary rounded-full animate-pulse" />
                    )}
                </span>
                {(isGrading || isEvaluating) ? (
                    <GradientText
                        colors={["#40ffaa", "#4079ff", "#40ffaa", "#4079ff", "#40ffaa"]}
                        animationSpeed={3}
                        showBorder={false}
                        className="text-xs font-medium"
                    >
                        {Math.round(progress)}%
                    </GradientText>
                ) : (
                    <span className={`text-xs font-medium ${getStatusColorClass()}`}>
                        {Math.round(progress)}%
                    </span>
                )}
            </div>
            <GradingProgressBar value={progress} status={status} />
        </div>
    );
};
